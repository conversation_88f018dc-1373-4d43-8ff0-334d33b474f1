<?php

namespace Tests\Integration;

use App\Models\User;
use App\Models\UserSession;
use App\Services\OktaService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Schema;
use Tests\TestCase;

/**
 * Integration test for actual Okta API calls.
 * 
 * This test requires real Okta credentials and will make actual API calls.
 * Set OKTA_INTEGRATION_TEST=true in your .env to enable these tests.
 * 
 * Required environment variables:
 * - OKTA_DOMAIN
 * - OKTA_CLIENT_ID  
 * - OKTA_CLIENT_SECRET
 * - OKTA_REDIRECT_URI
 * - OKTA_TEST_USERNAME (optional - for user lookup tests)
 * - OKTA_TEST_USER_ID (optional - for user lookup tests)
 */
class OktaIntegrationTest extends TestCase
{
    private OktaService $oktaService;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Skip if integration tests are not enabled
        $integrationTestEnabled = filter_var(env('OKTA_INTEGRATION_TEST', false), FILTER_VALIDATE_BOOLEAN);
        if (!$integrationTestEnabled) {
            $this->markTestSkipped('Okta integration tests are disabled. Set OKTA_INTEGRATION_TEST=true to enable.');
        }

        // Verify required environment variables
        $requiredVars = ['OKTA_DOMAIN', 'OKTA_CLIENT_ID', 'OKTA_CLIENT_SECRET', 'OKTA_REDIRECT_URI'];
        foreach ($requiredVars as $var) {
            if (empty(env($var))) {
                $this->markTestSkipped("Required environment variable {$var} is not set.");
            }
        }

        $this->createTables();
        $this->oktaService = new OktaService();
    }

    private function createTables(): void
    {
        Schema::create('users', function ($table) {
            $table->id();
            $table->string('name');
            $table->string('email')->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password')->nullable();
            $table->string('okta_user_id')->nullable()->unique();
            $table->string('okta_email')->nullable();
            $table->text('okta_profile_data')->nullable();
            $table->rememberToken();
            $table->timestamps();
        });

        Schema::create('user_sessions', function ($table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->text('okta_access_token')->nullable();
            $table->text('okta_refresh_token')->nullable();
            $table->text('okta_id_token')->nullable();
            $table->timestamp('okta_expires_at')->nullable();
            $table->string('app_token_hash')->nullable();
            $table->string('platform')->default('mobile');
            $table->string('okta_session_id')->nullable();
            $table->text('okta_user_data')->nullable();
            $table->string('state')->nullable();
            $table->string('code_verifier')->nullable();
            $table->string('code_challenge')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamp('last_activity_at')->nullable();
            $table->timestamps();
        });

        Schema::create('personal_access_tokens', function ($table) {
            $table->id();
            $table->morphs('tokenable');
            $table->string('name');
            $table->string('token', 64)->unique();
            $table->text('abilities')->nullable();
            $table->timestamp('last_used_at')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->timestamps();
        });
    }

    public function test_okta_service_configuration_is_valid()
    {
        // Test that all required configuration is present
        $this->assertNotEmpty(config('services.okta.domain'));
        $this->assertNotEmpty(config('services.okta.client_id'));
        $this->assertNotEmpty(config('services.okta.client_secret'));
        $this->assertNotEmpty(config('services.okta.redirect_uri'));

        // Test that domain format is correct
        $domain = config('services.okta.domain');
        $this->assertStringContainsString('.okta.com', $domain);
        $this->assertStringNotContainsString('https://', $domain);
    }

    public function test_pkce_challenge_generation()
    {
        $pkce = $this->oktaService->generatePkceChallenge();

        $this->assertIsArray($pkce);
        $this->assertArrayHasKey('code_verifier', $pkce);
        $this->assertArrayHasKey('code_challenge', $pkce);
        $this->assertEquals(128, strlen($pkce['code_verifier']));
        $this->assertNotEmpty($pkce['code_challenge']);

        // Verify code challenge is properly base64url encoded
        $this->assertMatchesRegularExpression('/^[A-Za-z0-9_-]+$/', $pkce['code_challenge']);
    }

    public function test_authorization_url_generation()
    {
        $state = 'test_state_' . time();
        $pkce = $this->oktaService->generatePkceChallenge();

        $authUrl = $this->oktaService->buildAuthorizationUrl($state, $pkce);

        // Verify URL structure
        $this->assertStringStartsWith('https://', $authUrl);
        $this->assertStringContainsString(config('services.okta.domain'), $authUrl);

        // Check for authorization endpoint with auth server ID
        $authServerId = config('services.okta.auth_server_id', 'default');
        $expectedPath = "/oauth2/{$authServerId}/v1/authorize";
        $this->assertStringContainsString($expectedPath, $authUrl);

        // Parse URL and verify parameters
        $urlParts = parse_url($authUrl);
        parse_str($urlParts['query'], $params);

        $this->assertEquals(config('services.okta.client_id'), $params['client_id']);
        $this->assertEquals('code', $params['response_type']);
        $this->assertEquals('openid profile email', $params['scope']);
        $this->assertEquals(config('services.okta.redirect_uri'), $params['redirect_uri']);
        $this->assertEquals($state, $params['state']);
        $this->assertEquals($pkce['code_challenge'], $params['code_challenge']);
        $this->assertEquals('S256', $params['code_challenge_method']);
    }

    /**
     * Test token introspection with a dummy token.
     * This will make an actual API call to Okta.
     */
    public function test_token_introspection_with_invalid_token()
    {
        $result = $this->oktaService->introspectToken('invalid_token_12345');

        $this->assertIsArray($result);
        $this->assertArrayHasKey('active', $result);
        $this->assertFalse($result['active']);
    }

    /**
     * Test token revocation with a dummy token.
     * This will make an actual API call to Okta.
     */
    public function test_token_revocation_with_invalid_token()
    {
        // This should return false for invalid tokens, but not throw an exception
        $result = $this->oktaService->revokeToken('invalid_token_12345');

        $this->assertIsBool($result);
        // Note: Okta might return true even for invalid tokens, so we just verify it doesn't crash
    }

    /**
     * Test the complete OAuth flow simulation.
     * This doesn't make actual OAuth calls but tests the flow structure.
     */
    public function test_oauth_flow_simulation()
    {
        // 1. Generate PKCE and state
        $pkce = $this->oktaService->generatePkceChallenge();
        $state = 'test_state_' . time();

        // 2. Build authorization URL
        $authUrl = $this->oktaService->buildAuthorizationUrl($state, $pkce);
        $this->assertNotEmpty($authUrl);

        // 3. Simulate what would happen after user authorizes
        // (In real flow, user would be redirected back with a code)
        
        // 4. Test that we can create a user from simulated Okta profile
        $simulatedOktaProfile = [
            'sub' => 'okta_integration_test_' . time(),
            'name' => 'Integration Test User',
            'email' => '<EMAIL>',
            'email_verified' => true,
            'preferred_username' => 'integration-test',
        ];

        $user = User::createOrUpdateFromOkta($simulatedOktaProfile);

        $this->assertInstanceOf(User::class, $user);
        $this->assertEquals('Integration Test User', $user->name);
        $this->assertEquals('<EMAIL>', $user->email);
        $this->assertNotNull($user->email_verified_at);
        $this->assertEquals($simulatedOktaProfile, $user->okta_profile_data);

        // 5. Test user session creation
        $userSession = UserSession::create([
            'user_id' => $user->id,
            'okta_access_token' => 'simulated_access_token',
            'okta_refresh_token' => 'simulated_refresh_token',
            'okta_expires_at' => now()->addHour(),
            'platform' => 'mobile',
            'okta_user_data' => $simulatedOktaProfile,
            'state' => $state,
            'code_verifier' => $pkce['code_verifier'],
            'is_active' => true,
            'last_activity_at' => now(),
        ]);

        $this->assertInstanceOf(UserSession::class, $userSession);
        $this->assertTrue($userSession->isOktaSessionValid());
        $this->assertEquals($simulatedOktaProfile, $userSession->okta_user_data);

        // 6. Test app token generation
        $appToken = $user->createToken('integration-test')->plainTextToken;
        $this->assertNotEmpty($appToken);

        // 7. Test session cleanup
        $userSession->deactivate();
        $this->assertFalse($userSession->fresh()->is_active);
    }

    /**
     * Test network connectivity to Okta.
     */
    public function test_okta_domain_connectivity()
    {
        $domain = config('services.okta.domain');

        // First test basic domain connectivity
        $basicUrl = "https://{$domain}";
        $context = stream_context_create([
            'http' => [
                'timeout' => 10,
                'method' => 'HEAD',
                'ignore_errors' => true,
            ]
        ]);

        $headers = @get_headers($basicUrl, 1, $context);
        $this->assertNotFalse($headers, "Could not connect to Okta domain: {$domain}");

        // Test that we get some kind of HTTP response (even if it's a redirect)
        $this->assertIsArray($headers);
        $this->assertNotEmpty($headers[0]);
        $this->assertStringContainsString('HTTP', $headers[0]);

        // Test that the domain is an Okta domain
        $this->assertStringContainsString('.okta.com', $domain);
    }

    protected function tearDown(): void
    {
        // Clean up any test data
        Schema::dropIfExists('personal_access_tokens');
        Schema::dropIfExists('user_sessions');
        Schema::dropIfExists('users');
        
        parent::tearDown();
    }
}
